# 构建文件夹和生成的文件
app/build/
.gradle/
.idea/
app/src/main/java/com/doctor/greendao/gen/
app/build/cache/
app/build/generated/
app/build/intermediates/
app/build/outputs/
app/product/
build/
*.iml
local.properties

# macOS系统文件
.DS_Store
**/.DS_Store

# Android Studio / IntelliJ IDEA相关文件
.roomodes
.vscode/

# 发布文件
app/huawei/release/
app/huawei/release/brzhongyi-huawei-release-v*.apk
output-metadata.json

# 特定目录下的DS_Store文件
app/huawei/.DS_Store
app/.DS_Store
app/src/.DS_Store
app/src/huawei/.DS_Store
app/src/huawei/assets/.DS_Store
app/src/main/.DS_Store
app/src/main/java/com/doctor/br/adapter/.DS_Store
app/src/main/java/com/doctor/br/adapter/medical/.DS_Store
app/src/main/res/.DS_Store
app/src/main/res/drawable-xxhdpi/.DS_Store
app/src/main/res/layout/.DS_Store
app/src/main/res/xml/.DS_Store
baselibrary/.DS_Store

*.class
# 其他临时文件和日志
*.log
tmp/
temp/
# Aider AI编辑器相关文件
.aider*
*.aider.chat
*.aider.input
*.aider.output
.aider/
.aider.tags.cache.v3/

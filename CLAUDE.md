# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

always response in 中文。

修改完成后，不要进行 git 数据提交，不要提交！！！ 同样不用构建和运行项目，只需要检测语法错误即可。 

## Project Overview

This is **必然中医** (BrZhongYi) - a Traditional Chinese Medicine (TCM) mobile application for Android. The app enables TCM practitioners to manage prescriptions, communicate with patients, and handle medicine orders through real-time messaging and comprehensive medical tools.

**Application ID:** `com.doctor.yy`  
**Current Version:** 4.6.3 (Build 463)  
**Main Package:** `com.doctor.br`

## Build Commands

### Development Build
```bash
./gradlew assembleProductDebug
```

### Release Build 
```bash
./gradlew assembleProductRelease
```

### Clean Build
```bash
./gradlew clean
./gradlew assembleProductDebug
```

### Build for Huawei App Store
```bash
./gradlew assembleHuaweiRelease
```

## Project Architecture

### Module Structure
- **app/** - Main application module with all business logic
- **baselibrary/** - Shared utilities and base classes
- **SwipeLibrary/** - Custom swipe refresh component

### Key Technical Stack
- **Database:** GreenDAO 3.3.0 (schema version 10)
- **Network:** Retrofit + RxJava + OkHttp
- **Real-time Communication:** Custom Netty 4.1.8 WebSocket client
- **Image Loading:** Glide 4.0.0
- **DI:** ButterKnife 10.0.0
- **Push:** JPush 5.4.0
- **Video:** ExoPlayer 2.14.1
- **Social Sharing:** MobSDK with WeChat/QQ integration

### Core Application Structure
```
com.doctor.br/
├── activity/          # Feature-based activities
│   ├── chatmain/     # Real-time messaging system
│   ├── manage/       # Practice management tools
│   ├── medical/      # Prescription and medicine features
│   └── mine/         # User profile and settings
├── netty/            # WebSocket communication layer
├── db/               # GreenDAO database entities
├── bean/             # Data models and DTOs
└── utils/            # Shared utilities
```

## Database Management

### GreenDAO Configuration
- **Schema Version:** 10 (increment when changing entity structure)
- **Generated Files:** `com.doctor.greendao.gen` package
- **Generation Command:** Build automatically generates DAO classes

### Database Schema Updates
When modifying entity classes:
1. Update `@Entity` annotations
2. Increment `schemaVersion` in `app/build.gradle` (line 115)
3. Clean and rebuild project

## Real-time Communication

The app uses a custom Netty-based WebSocket client for real-time messaging:
- **Implementation:** `com.doctor.br.netty` package
- **Protocol:** Custom message format for TCM consultation
- **Connection Management:** Auto-reconnection and heartbeat handling

## Version Management

Version information is stored in `project.properties`:
- Update `versionCode` for each release build
- Update `versionName` following semantic versioning
- APK naming: `brzhongyi-{flavor}-{buildType}-v{versionName}.apk`

## Build Flavors

### Product Flavors
- **product** - General app store distribution (`channel = "common"`)
- **huawei** - Huawei AppGallery distribution (`channel = "huawei"`)

### Key Build Configuration
- **Min SDK:** 16
- **Target SDK:** 30
- **Compile SDK:** 30
- **NDK Support:** armeabi, armeabi-v7a, arm64-v8a
- **ViewBinding:** Enabled
- **Multidex:** Enabled

## Third-party Integration Keys

### JPush Configuration
- **App Key:** `3be91d69be0a214eaa562a2a`
- **Package:** Uses `applicationId` from build config

### Social SDK (MobSDK)
- **WeChat App ID:** `wx266e4342efb1a84b`
- **QQ App ID:** `100424468`
- **Configuration:** See `MobSDK` block in `app/build.gradle`

## Code Conventions

### Package Organization
- Activities grouped by feature areas (chat, manage, medical, mine)
- Fragments follow same grouping as activities
- Adapters in dedicated `adapter` package
- Database entities in `db` package with GreenDAO annotations

### Naming Conventions
- Activities: `{Feature}{Action}Activity` (e.g., `ChatMainActivity`)
- Fragments: `{Feature}Fragment` (e.g., `ContactsFragment`)
- Layouts: `activity_{feature}_{action}.xml`, `fragment_{feature}.xml`
- Database entities: Plain class names with `@Entity` annotation

## Dependencies Management

### Chinese Mirror Repositories
The project uses Alibaba Cloud mirrors for faster dependency resolution in China:
- Aliyun Google repository
- Aliyun JCenter repository
- MobSDK Maven repository

### Key Library Versions
- Kotlin: 1.6.21
- Android Gradle Plugin: 4.2.2
- GreenDAO: 3.3.0
- ExoPlayer: 2.14.1
- JPush: 5.4.0

## Development Notes

### Memory Configuration
- **Gradle JVM:** `-Xmx2560M` (configured in `gradle.properties`)
- **Dex Heap:** `2048M` with jumbo mode enabled
- **Memory Leak Detection:** LeakCanary integrated for debug builds

### ProGuard/R8
- **Minification:** Currently disabled in release builds
- **Resource Shrinking:** Disabled
- **R8:** Explicitly disabled (`android.enableR8=false`)

### Signing
- **Keystore:** `keystore.jks` in project root
- **Alias:** `brzhongyi`
- **Same configuration** for debug and release builds


## 对应的iOS 版本说明 

目录 /Users/<USER>/repository/brzy/brzy_ios 中存放的是项目对应的 iOS 版本，在没有特殊说明下，两者的业务逻辑也 UI 界面等应该保持高度一致。 
你有权限可以通过filesystem 的 mcp server 进行读取和编辑。 

## 文档目录说明
目录 /Users/<USER>/repository/brzy/brzy_docs 中保存的是项目的文档目录。 你有权限可以通过 filesystem 的 mcp server 进行读取和编辑。 
一些详细的说明和重要修改，需要进行保存和记录，使用 markdown 的显示进行保存，文件名以中文说明和日期来命名。 
package com.doctor.br.netty.client;

import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.doctor.br.app.AppContext;
import com.doctor.br.netty.NettyResultCode;
import com.doctor.br.netty.impl.DataReceiverImpl;
import com.doctor.br.netty.listener.DataReceiver;

import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.MD5Utils;
import org.newapp.ones.base.utils.PhoneManager;

import java.util.HashMap;
import java.util.Map;



/**
 * 消息服务器客户终端封装
 */
public class NettyClient implements DataReceiver {
    private static Object lock = new Object();
    private static volatile long counter = 0;
    private long timeout = 20 * 1000;//最后读取数据的时间
    private volatile NettyConnection connection;
    private volatile PingThread pingThread;
    private String username;
    private String password;
    private String userid;
    private volatile boolean acceptMessage;
    private volatile boolean logined;
    private static NettyClient mInstance;
    private Map user;
    private DataReceiver mDataReceiver;
    private String deviceId = "";


    private NettyClient(String username, String pwd) {
        this.username = username;
        this.password = pwd;
        this.mDataReceiver = new DataReceiverImpl(this);
        this.deviceId = AppContext.getInstances().getIMEI();
    }

    public DataReceiver getDataReceiver() {
        return mDataReceiver;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * 获取一个NettyClient实例对象
     *
     * @param username
     * @param pwd
     * @return
     */
    public static NettyClient getInstance(String username, String pwd) {
        synchronized (NettyClient.class) {
            if (mInstance == null) {
                mInstance = new NettyClient(username, pwd);
            } else {
                mInstance.username = username;
                mInstance.password = pwd;
            }
            return mInstance;
        }
    }


    public Map getUser() {
        return user;
    }

    /**
     * 请多次调用这个方法.
     */
    public void start() {
        synchronized (lock) {
            if (connection != null && connection.isConnected()) {
                return;
            } else {
                if (pingThread != null) {
                    pingThread.canceled = true;
                }
                if (connection != null) {
                    connection.close();
                }
                this.logined = false;
                this.userid = null;
                connection = NettyConnection.getInstance(this, timeout);
                connection.connect();
                pingThread = new PingThread();
                pingThread.start();
//                SendMsgQueue.getInstance().start();
            }
        }
    }

    /**
     * 终止线程，关闭连接
     */
    public void stop() {
        synchronized (lock) {
            if (pingThread != null) {
                pingThread.canceled = true;
            }
            if (connection != null) {
                connection.close();
            }
            acceptMessage = false;
        }
    }


    /**
     * 判断客户端是否与服务端连接
     *
     * @return
     */
    public boolean isLogined() {
        return logined;
    }


    public long getCounter() {
        synchronized (lock) {
            return ++counter;
        }
    }


    /**
     * 登录到消息服务器
     */
    private void login() {
        acceptMessage = false;
        String pwdStr = password;
        if (!TextUtils.isEmpty(password)) {
            if (password.length() >= 6) {
                pwdStr = "pwd:" + MD5Utils.MD5Upper32(password) + ",device:" + AppContext.getInstances().getIMEI() + ",deviceToken:" + deviceId;
            } else {
                pwdStr = "sms:" + password + ",device:" + AppContext.getInstances().getIMEI() + ",deviceToken:" + deviceId;
            }
        } else {
            pwdStr = "device:" + AppContext.getInstances().getIMEI() + ",deviceToken:" + deviceId;
        }
        String json = "{'password':'" + pwdStr + "','code':'0002','id':'" + getCounter() + "','device':'Smack',"
                + "'username':'" + username + "','uuid':'" +AppContext.getInstances().getIMEI()+"',"+
                "'status':'BUSY','userType':'1','os':'ANDROID'," +
                "'osInfo':'" + getHandSetInfo() + "'}";
        write(json, true);
    }

    /**
     * 获取设备信息
     *
     * @return
     */
    private String getHandSetInfo() {
        String handSetInfo =
                "MODEL:" + android.os.Build.MODEL +
                        ",VERSION:" + android.os.Build.VERSION.RELEASE +
                        ",NETWORK:" + PhoneManager.getNetworkType();
        return handSetInfo;
    }

    /**
     * 发送ping消息
     */
    private void ping() {
        if (logined) {
            LogUtils.i(this.getClass(), "---------------ping--------------");
            String json = "{'userid':'" + userid + "','code':'0007','id':'" + getCounter() + "'}";
            write(json);
        }
    }

    /**
     * 请求开始接收消息
     */
    private void acceptMessage() {
        if (logined) {
            String json = "{'userid':'" + userid + "','code':'0025','id':'" + getCounter() + "'}";
            write(json);
        }
    }


    /**
     * 请求开始接收消息
     */
    public void startAcceptMessage() {
        this.acceptMessage = true;
        acceptMessage();
    }


    /**
     * 将消息写入到服务器
     *
     * @param msg 消息json串
     */
    public void write(String msg) {
        _write(msg, false);
    }

    /**
     * 将消息写入到服务器
     *
     * @param msg     消息json串
     * @param isLogin
     */
    public void write(String msg, boolean isLogin) {
        _write(msg, isLogin);
    }

    /**
     * 将消息写入到服务器
     *
     * @param msg 消息json串
     */
    private void _write(String msg, boolean isLogin) {
        if ((isLogin || logined) && connection != null) {
            LogUtils.i(NettyClient.class, "send -> " + msg);
            connection.write(msg);
        }
    }


    /**
     * 接收到服务器返回的数据的处理方法
     *
     * @param data 返回的数据
     */
    @Override
    public void received(String data) {
        try {
            if (NettyConnection.CONNECTED.equals(data)) {
                login();
                return;
            }
//            HashMap<String, Object> map = (HashMap<String, Object>) JSON.parse(data);
            HashMap<String, Object> map = JSON.parseObject(data, new TypeReference<HashMap<String, Object>>() {
            });
            String code = (String) map.get("code");
            String resultCode = (String) map.get("resultCode");
            if ("0002".equals(code)) {
                if (NettyResultCode.SUCCESS.equals(resultCode)) {
                    logined = true;
                    user = (Map) map.get("user");
                    userid = (String) user.get("id");
                } else {
                    logined = false;
                }
            }
            if (mDataReceiver != null) {
                mDataReceiver.received(data);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * ping线程
     */
    private class PingThread extends Thread {
        private volatile boolean canceled;

        @Override
        public void run() {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e1) {
            }
            while (!canceled) {
                try {
                    if (connection != null && connection.isConnected()) {
                        long interval = System.currentTimeMillis() - connection.getLastReadTime();
                        if (interval > (timeout * 2 / 3)) {
                            ping();
                        }
                    } else {
                        NettyClient.this.start();
                    }
                    Thread.sleep(2000);
                } catch (Throwable e) {
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e1) {
                    }
                }
            }
        }
    }
}
